import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, I18N_COMON_PAGENAME, RENDER_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { getRequestData } from '@/utils/urlUtil';

export const pageConfig = {
  prefix: I18N_COMON_PAGENAME.CALL_PARAM,
  cardTitle: 'caseList',
  // 页面接口请求
  urlObj: {
    list: urlConstants.CASE_BASE_INFO.LIST,
    getRequestData,
  },
  // 搜索
  searchSource: [
    {
      value: 'caseCode',
      label: 'caseCode',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'model',
      label: 'model',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.CASEMANAGE_MODEL,
    },
    {
      value: 'dayNum',
      label: 'dayNum',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'caseAmt',
      label: 'caseAmt',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'currState',
      label: 'currState',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.PARAM_STATUS,
    },
  ],

  // 列表
  columns: [
    // {
    //   title: 'paramIndex',
    //   dataIndex: 'paramIndex',
    //   key: 'paramIndex',
    //   width: 80,
    // },
    {
      title: 'caseCode',
      dataIndex: 'caseCode',
      key: 'caseCode',
      width: 150,
    },
    {
      title: 'orgCustNbr',
      dataIndex: 'orgCustNbr',
      key: 'orgCustNbr',
      width: 150,
    },
    {
      title: 'custName',
      dataIndex: 'custName',
      key: 'custName',
      width: 150,
    },
    {
      title: 'model',
      dataIndex: 'model',
      key: 'model',
      width: 150,
      data: DICT_CONSTANTS.CASEMANAGE_MODEL,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'CASEMANAGE_MODEL',
    },
    {
      title: 'icType',
      dataIndex: 'icType',
      key: 'icType',
      width: 150,
    },
    {
      title: 'custIc',
      dataIndex: 'custIc',
      key: 'custIc',
      width: 150,
    },
    {
      title: 'currState',
      dataIndex: 'currState',
      key: 'currState',
      width: 150,
    },
    {
      title: 'caseState',
      dataIndex: 'caseState',
      key: 'caseState',
      width: 150,
    },
    {
      title: 'callTotal',
      dataIndex: 'callTotal',
      key: 'callTotal',
      width: 150,
    },
    {
      title: 'dteIntoCollection',
      dataIndex: 'dteIntoCollection',
      key: 'dteIntoCollection',
      width: 150,
    },
    {
      title: 'dteOutCollection',
      dataIndex: 'dteOutCollection',
      key: 'dteOutCollection',
      width: 150,
    },
    {
      key: 'updateTime',
      title: 'updateTime',
      dataIndex: 'updateTime',
      width: 160,
      valueType: RENDER_TYPE.DateTime,
    },
    {
      key: 'updateUser',
      title: 'updateUser',
      dataIndex: 'updateUser',
      width: 150,
    },
  ],
};

// 需要查询的参数类型
export const dictEnum = {
  // crcdOrgNo: DICT_CONSTANTS.DICT_ENUM_MAP.crcdOrgNo,
};
