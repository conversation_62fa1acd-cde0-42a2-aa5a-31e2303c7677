import { useState, useEffect } from 'react';
import { Outlet, useLocation, useNavigate, useSearchParams } from 'ice'; // react-router-dom
import store from '@/store';
import { IntlProvider } from 'react-intl'; // 国际化
import { ConfigProvider, Layout, Spin } from 'antd';
import type { Locale } from 'antd/es/locale';
import en from 'antd/locale/en_US';
import zhCN from 'antd/locale/zh_CN';
import zhTW from 'antd/locale/zh_TW';
import 'dayjs/locale/zh-cn';
import 'dayjs/locale/zh-tw';
import 'dayjs/locale/en';
import { messages } from '@/locales';
import Sider from '@/layouts/sider';
import Header from '@/layouts/header';
import { SESSION, LOCAL, LANGUAGE_LIST } from '@/constants/publicConstant';
import { greenTheme, themeColor, themeMap } from '@/constants/styleConstant';
import { getUserInfo } from '@/services/menu';
import { findRootNode } from '@/utils/comUtil';
import { TSysMenuItem } from '@/types/TCommon';

// antd国际化枚举
const languagePackage = {
  en,
  'zh-cn': zhCN,
  'zh-tw': zhTW,
};
// 全局设置样式变量
const setThemeColor = (theme) => {
  if (theme) {
    document.documentElement.style.setProperty('--color-one', theme.colorOne);
    document.documentElement.style.setProperty('--color-two', theme.colorTwo);
    document.documentElement.style.setProperty('--color-three', theme.colorThree);
    document.documentElement.style.setProperty('--color-main', theme.colorMain);
    document.documentElement.style.setProperty('--color-five', theme.colorFive);
    document.documentElement.style.setProperty('--color-six', theme.colorSix);
    // 继续覆盖其他颜色字段
  }
};
const theme = localStorage.getItem(LOCAL.THEME) || themeColor.blue;
setThemeColor(themeMap[theme]);

export default function GlobalLayout() {
  // 内部变量
  const menuCodeIndex = sessionStorage.getItem(SESSION.codeIndex) || '';
  const initialLocale: string = localStorage.getItem(LOCAL.LOCALE) || LANGUAGE_LIST.CN.locale;
  const initialPackage = languagePackage[initialLocale];
  const [codeIndex, setCodeIndex] = useState(menuCodeIndex);
  const [loading, setLoading] = useState<boolean>(false);
  const [locale] = useState<Locale>(initialPackage);
  const location = useLocation();
  const navigate = useNavigate();
  const colorPrimary = theme || greenTheme.colorMain;
  const [searchParams] = useSearchParams();
  const [userState, userDispatchers] = store.useModel('user');
  const token = sessionStorage.getItem(SESSION.token);

  // 副作用
  // 国际化
  useEffect(() => {
    localStorage.setItem(LOCAL.LOCALE, locale.locale);
  }, [locale]);

  // 主题
  useEffect(() => {
    const theme = localStorage.getItem(LOCAL.THEME);
    if (!theme) {
      localStorage.setItem(LOCAL.THEME, colorPrimary);
    }
  }, []);

  useEffect(() => {
    !loading && getUserInfoData(token);
  }, [token]);

  useEffect(() => {
    // 权限是否初始化完成
    if (userState.initDone) {
      // 判断有无路由路径，无则打开第一个菜单下的第一个页面
      try {
        // 找到根节点
        const { menuId } = userState.menuData.filter((item) => item.menuRouteUrl == location.pathname)[0];
        const rootNode = findRootNode(userState.menuData, menuId, 'menuId', 'parentMenuId');
        const { menuId: rootMenuId } = rootNode as TSysMenuItem;
        if (rootMenuId) {
          handleSetCodeIndex(rootMenuId);
        }
      } catch (error) {
        const routeData = userState.menuData.filter((item) => item.type == 'link')[0].menuRouteUrl;
        // 获取当前第一个一级菜单的menuId，作为codeIndex
        const val = userState.menuTree[0].menuId;
        handleSetCodeIndex(val);
        navigate(routeData as string);
      }
    }
  }, [userState.initDone, location.pathname]);

  // 获取用户信息及权限信息
  const getUserInfoData = async (token): Promise<void> => {
    try {
      if (!token) {
        navigate('/login');
        return;
      }
      setLoading(true);
      // const userInfo = await getUserInfo({});
      // if (userInfo) {
      //   userDispatchers.updateUserInfo(userInfo);
      // }
      await userDispatchers.getUserPermissionInfo();
      sessionStorage.setItem(SESSION.token, '212');
    } catch (e) {
      sessionStorage.clear();
      console.log('校验失败', e);
    } finally {
      setLoading(false);
    }
  };

  // 事件处理
  // 切换一级菜单
  const handleSetCodeIndex = (e): void => {
    setCodeIndex(e);
    sessionStorage.setItem(SESSION.codeIndex, e);
  };

  return (
    <IntlProvider locale={locale.locale} messages={messages[locale.locale]}>
      <ConfigProvider
        locale={locale}
        theme={{
          token: { colorPrimary },
          components: {
            Button: { colorPrimary },
          },
        }}
      >
        {['/login'].includes(location.pathname) ? (
          <Outlet />
        ) : (
          <Layout className="height100">
            <Header onSelect={(e) => handleSetCodeIndex(e)} />
            <Layout className="flex-row">
              <Sider />
              {loading ? (
                <Spin size="large" className="flex-row flex-align-center flex-justify-center width100" />
              ) : (
                userState.initDone && (
                  <Layout.Content className="common-padding flex-col">
                    <Outlet />
                  </Layout.Content>
                )
              )}
            </Layout>
          </Layout>
        )}
      </ConfigProvider>
    </IntlProvider>
  );
}
